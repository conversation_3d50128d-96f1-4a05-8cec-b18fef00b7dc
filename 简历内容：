吴喜才
电话：15796433831 邮箱： <EMAIL>
⼯作经历
深圳鑫⽹泰科技有限公司 - 前端leader (2024年3⽉ -2025年04⽉ )
- 组建并领导前端开发团队，制定技术规划和开发流程，确保项⽬⾼质量按时交付
- 主导前端技术架构设计和技术栈选型，推动关键技术决策，解决复杂技术难题
- 设计并实现核⼼业务模块，优化前端性能，提升应⽤响应速度和⽤户体验
- 与产品、设计和后端团队紧密协作，确保产品需求准确实现和技术⽅案落地
兴盛优选 - 前端开发⼩组负责⼈ (2021年5⽉ -2023年12⽉ )
- 领导前端团队进⾏多个核⼼业务项⽬的开发，制定技术⽅案和开发计划
- 主导前端架构升级和技术栈优化，提升开发效率和应⽤性能。
- 设计并实现复杂功能模块，解决技术难点，确保产品质量和⽤户体验
- 建⽴前端开发规范和代码审查机制，提⾼代码质量和可维护性。
货拉拉 - ⾼级前端⼯程师 (2018年9⽉ -2020年11⽉ )
- 负责核⼼业务模块的技术⽅案设计和开发实现，解决关键技术难题
- 构建基于Sentry的前端监控体系，设计错误追踪与告警机制，提升问题定位效率
- 参与前端架构优化和性能提升⼯作，改善⽤户体验和⻚⾯加载速度
- 指导初级⼯程师，组织技术分享，提升团队整体技术⽔平
酷狗⾳乐 - 前端⼯程师 (2015年5⽉ -2018年5⽉ )
- 负责⾳乐平台核⼼功能模块的设计与开发，确保产品功能稳定和⽤户体验
- 设计并实现BFF中间层服务，优化⾸屏加载性能和服务端渲染
- 协助解决跨平台兼容性问题，确保应⽤在不同设备上的⼀致体验
⼴州软通动⼒有限公司 - 前端⼯程师 (2013年9⽉ - 2015年3⽉)
⼴州康爱多医药连锁有限公司 - 前端⼯程师 (2012年6⽉ - 2013年8⽉)核⼼项⽬
项⽬⼀：兴盛优选电商平台
技术栈：微信程序
⻆⾊：前端项⽬负责⼈
关键职责：
架构设计与优化：基于 微信程序技术栈，设计可扩展、 ⾼性能的前端架构。
团队管理与协作：负责前端团队⼈员分⼯、任务分配与进度把控，组织技术分享与代码
review，提升团队整体开发能⼒与代码质量。
项⽬⼆：兴盛优选供应链物流系统
技术栈：Vue2/3 + ES6/TypeScript + qiankun
⻆⾊：前端负责⼈+项⽬管理
价值： ⽀撑⽇ 均10万+仓储操作，降低30%⼈⼯调度成本
关键职责：业务需求梳理，项⽬架构搭建+核⼼模块开发
业务价值： ⽀撑全国30+仓库统⼀管理。全程可视化追踪（从⼊库到签收）PDA移动端操作
替代纸质单据，仓库⼈员作业效率
项⽬三：兴盛优选OA系统
技术栈：Vue3 + qiankun
⻆⾊：前端负责⼈+项⽬管理
关键职责：业务需求梳理，项⽬架构搭建+核⼼模块开发
业务价值： ⽀撑2万+员⼯，可⽤性99.99%
关键技术：
微前端qiankun架构落地10+⼦系统
UI组件库建设（50+组件）
项⽬四：货拉拉客服中⼼系统
技术栈：Vue2 + iView
⻆⾊：前端负责⼈
关键职责：业务需求梳理，项⽬架构搭建+核⼼模块开发
业务价值： ⽀持⽇ 均处理30万+客服⼯单，系统可⽤性达99.95%，实时监控⼤屏帮助管理
端决策效率提升40%项⽬五：货拉拉前端监控系统
技术栈：Sentry 开源项⽬
⻆⾊：项⽬负责⼈
价值：帮助开发⼈员快速定位线上问题。错误捕获率98%.故障定位15分钟内
项⽬六：酷狗/HiFi客户端web⻚⾯
技术栈：Hybrid（C++ web）
⻆⾊：前端开发⼯程师
关键职责：业务需求梳理,核⼼模块开发
业务价值： ⾸款Hi-Res认证Web⾳乐应⽤
项⽬七：Node.js SSR官⽹
技术栈：Node.js + express + Redis
⻆⾊：项⽬负责⼈+架构设计+核⼼开发
关键职责：业务需求梳理,核⼼模块开发
业务价值：提升接⼝性能，提升系统稳定性，降低服务器成本，降低开发维护成本
技术栈与技能
核⼼技术
- 前端框架：React, Vue, ES6/TypeScript
- ⼯程化⼯具：Webpack, Vite, ESLint，jenkins
- 性能优化：性能监控、缓存策略、资源加载优化
- 架构能⼒：微前端、中台技术、DevOps实践
教育背景
- MBA, 兰州⼤学(2021/09-2024/06)
- 计算机科学与技术, 湖南信息学院(2009/09-2012/06)