# 吴先生 | 高级前端开发工程师
**Vue3技术专家 | 前端组件库架构师 | 10年+前端开发经验 | 兰州大学MBA硕士**

---

## 📞 联系方式
- **手机：** 13049380563  
- **邮箱：** <EMAIL>
- **现居地：** 深圳  
- **求职状态：** 立即到岗
- **期望薪资：** 30K-45K

---

## 🎯 核心优势
- **✅ 学历匹配：** 全日制本科+硕士学历，计算机相关专业
- **✅ 经验超标：** 10年+前端开发经验（远超3年要求）
- **✅ Vue3专家：** 多个大型Vue3项目开发经验，深度掌握Composition API
- **✅ 组件库专家：** 从0到1构建企业级组件库，被5+业务线采用
- **✅ 架构能力：** 独立完成复杂前端架构设计，支撑千万级用户访问
- **✅ Node.js能力：** 具备Node.js编写脚本工具和服务端应用能力

---

## 💼 工作经历

### 深圳鑫网泰科技有限公司 | 前端架构师/高级前端开发工程师
**2024.03 - 2025.04 | 14个月 | 薪资：35K**

**核心职责：**
- 负责前端技术架构设计，制定前端开发规范和最佳实践
- 主导Vue3技术栈升级和前端工程化建设
- 设计并开发企业级前端组件库，提升开发效率
- 与产品、设计、后端团队协作，确保项目按时交付

**关键成果：**
- 🏆 **组件库建设：** 从0到1构建Vue3组件库，包含50+组件，开发效率**提升40%**
- 🏆 **架构升级：** 主导前端架构现代化升级，构建速度**提升60%**
- 🏆 **性能优化：** 大数据量渲染性能**提升80%**，支持10万+数据展示
- 🏆 **代码质量：** 建立完善的代码规范体系，代码复用率**提升50%**

### 湖南兴盛优选网络科技有限公司 | 高级前端开发工程师
**2021.05 - 2023.12 | 2年8个月 | 薪资：22K→28K**

**核心职责：**
- 负责千万级用户电商平台前端开发和架构优化
- 参与Vue2到Vue3的技术栈升级工作
- 开发复杂业务功能模块，保证代码质量和性能

**关键成果：**
- 🏆 **大用户量支撑：** 支撑**1000万+**日活用户的稳定访问
- 🏆 **技术升级：** 主导Vue2到Vue3升级，影响10+个业务线
- 🏆 **性能提升：** 页面加载速度**提升50%**，用户体验显著改善

---

## 🚀 核心项目经历

### 项目一：企业级Vue3组件库 | 2024.03-2025.04
**【项目规模】** 50+组件，5+业务线使用，20+开发者贡献  
**【技术栈】** Vue3 + TypeScript + Vite + VitePress + Jest

**【项目背景】**
公司多个业务线使用不同的UI组件，存在重复开发、设计不统一、维护成本高等问题。

**【解决方案】**
- **组件设计系统：** 建立完整的设计规范，包含颜色、字体、间距、动效等标准
- **Vue3 + TypeScript：** 使用Composition API重构组件逻辑，提供完整的类型支持
- **工程化建设：** 使用Vite构建，支持ES Module、CommonJS多种格式输出
- **文档与测试：** VitePress构建组件文档，Jest + Vue Test Utils实现单元测试

**【核心组件开发】**
- **基础组件：** Button、Input、Select、DatePicker等20+个基础组件
- **业务组件：** Table、Form、Upload、Chart等15+个复杂业务组件
- **布局组件：** Layout、Grid、Space等10+个布局组件
- **反馈组件：** Message、Modal、Drawer等5+个交互反馈组件

**【量化成果】**
- ✅ 组件库被5个业务线采用，覆盖率100%
- ✅ 开发效率提升40%，重复代码减少80%
- ✅ 设计一致性提升，用户体验统一
- ✅ 单元测试覆盖率85%+，组件质量可靠

### 项目二：电商ERP前端架构重构 | 2024.03-2025.04
**【项目规模】** 8个子应用，100+页面，支持100万+SKU管理  
**【技术栈】** Vue3 + qiankun + TypeScript + Pinia + Ant Design Vue

**【项目背景】**
传统ERP系统基于Vue2开发，存在性能瓶颈、代码耦合度高、多团队协作困难等问题。

**【架构设计】**
- **微前端架构：** 使用qiankun实现微前端，拆分为8个独立子应用
- **Vue3升级：** 全面升级到Vue3，使用Composition API重构业务逻辑
- **状态管理：** 使用Pinia替代Vuex，实现更简洁的状态管理
- **构建优化：** 使用Vite替代Webpack，构建速度提升60%

**【性能优化】**
- **虚拟滚动：** 实现高性能虚拟滚动表格，支持100万+数据展示
- **懒加载：** 实现路由级和组件级懒加载，首屏时间减少70%
- **缓存策略：** 实现多级缓存，包括组件缓存、接口缓存、静态资源缓存
- **内存优化：** 优化组件生命周期，避免内存泄漏问题

**【量化成果】**
- ✅ 大数据渲染性能提升80%，操作流畅度显著改善
- ✅ 构建速度提升60%，开发效率大幅提升
- ✅ 代码复用率提升50%，维护成本降低
- ✅ 系统稳定性99.9%+，用户满意度显著提升

### 项目三：兴盛优选电商平台Vue3升级 | 2021.05-2023.12
**【项目规模】** 千万级用户，30+城市覆盖，100+页面重构  
**【技术栈】** Vue3 + TypeScript + Vant + Element Plus + ECharts

**【项目背景】**
兴盛优选核心电商平台基于Vue2开发，需要升级到Vue3以获得更好的性能和开发体验。

**【升级策略】**
- **渐进式升级：** 采用渐进式升级策略，确保业务连续性
- **组件重构：** 使用Composition API重构核心业务组件
- **性能优化：** 利用Vue3的性能优势，优化大列表渲染
- **类型安全：** 引入TypeScript，提升代码质量

**【核心功能模块】**
- **商品管理：** 商品列表、详情、搜索、筛选等功能
- **订单系统：** 订单创建、支付、状态跟踪、售后等流程
- **用户中心：** 用户信息、收货地址、优惠券、积分等功能
- **数据大屏：** 销售数据、用户行为、运营指标等可视化

**【量化成果】**
- ✅ 支撑1000万+日活用户稳定访问
- ✅ 页面加载速度提升50%，用户体验优秀
- ✅ 开发效率提升30%，bug率降低40%
- ✅ 系统可用性99.95%，业务连续性保障

### 项目四：供应链管理系统前端开发 | 2022.01-2023.12
**【项目规模】** 30+仓储中心，日处理订单10万+，覆盖全国  
**【技术栈】** Vue3 + qiankun + Vant + 百度地图API + WebSocket

**【项目背景】**
构建全国性供应链物流管理系统前端，支持复杂的仓储和物流业务流程。

**【架构特色】**
- **微前端实践：** 使用qiankun实现仓库管理、运输调度、数据报表等模块独立开发
- **移动端适配：** 使用Vue3 + Vant开发移动端PDA操作界面
- **地图集成：** 集成百度地图API实现配送路径规划和实时追踪
- **实时通信：** 基于WebSocket实现订单状态实时更新

**【量化成果】**
- ✅ 支持30+仓储中心稳定运行
- ✅ 移动端操作效率提升60%
- ✅ 配送准确率99.5%+，客户满意度高
- ✅ 系统响应时间<2秒，操作体验流畅

---

## 🛠️ 技术技能详情

### 前端技术基础（精通）
- **HTML5/CSS3：** 语义化标签、Flexbox、Grid、CSS3动画、响应式设计
- **JavaScript：** ES6+语法、异步编程、模块化、函数式编程
- **TypeScript：** 类型系统、泛型、装饰器、高级类型推导

### 主流前端框架（精通）
- **Vue生态：** Vue2/Vue3、Vue Router、Vuex/Pinia、Vue CLI/Vite
- **React生态：** React 16+、Hooks、Redux、React Router、Next.js
- **框架原理：** 虚拟DOM、响应式原理、组件通信、生命周期

### Vue3专项技能（专家级）
- **Composition API：** 深度掌握setup、ref、reactive、computed、watch等API
- **性能优化：** 懒加载、虚拟滚动、内存管理、渲染优化
- **生态工具：** Vite、Pinia、VueUse、Vue DevTools
- **最佳实践：** 组件设计、状态管理、代码组织、测试策略

### 前端组件库开发（专家级）
- **设计系统：** 设计规范制定、主题定制、国际化支持
- **组件架构：** 组件分层、API设计、扩展性设计
- **工程化：** 构建配置、文档生成、自动化测试、发布流程
- **质量保障：** 单元测试、E2E测试、视觉回归测试

### Node.js开发（熟练）
- **基础能力：** 文件系统、网络编程、进程管理、模块系统
- **框架应用：** Express、Koa、Nest.js
- **工具开发：** 构建脚本、CLI工具、自动化脚本
- **服务端应用：** API开发、中间件、数据库操作

### 前端工程化（熟练）
- **构建工具：** Webpack、Vite、Rollup、Parcel
- **代码质量：** ESLint、Prettier、Husky、Commitizen
- **测试框架：** Jest、Vue Test Utils、Cypress、Playwright
- **CI/CD：** GitLab CI、GitHub Actions、自动化部署

---

## 🎓 教育背景
**兰州大学 | 工商管理硕士(MBA) | 2021.09-2024.06**
- 985工程重点大学，全日制硕士学历
- 专业方向：技术管理与创新
- 核心课程：项目管理、技术创新、数据分析

**湖南信息学院 | 计算机科学与技术学士 | 2009.09-2012.06**  
- 全日制本科学历，计算机相关专业
- 专业基础：数据结构、算法、软件工程、数据库

---

## 🏆 个人亮点
- **🎯 技能完全匹配：** Vue3、组件库、前端架构等核心要求100%匹配
- **📈 经验远超要求：** 10年前端经验，远超3年要求
- **🚀 架构设计能力：** 独立完成复杂前端系统架构设计
- **💡 技术深度：** 对框架原理有深度理解，能解决复杂技术问题
- **🔧 工程化实践：** 具备完整的前端工程化建设经验
- **👥 团队协作：** 优秀的跨部门沟通协作能力
- **📝 代码质量：** 良好的代码风格和注释习惯

---

## 💬 自我评价
作为一名拥有10年+前端开发经验的工程师，我在Vue3技术栈方面有着深厚的积累和丰富的实战经验。曾主导多个大型项目的前端架构设计，具备从组件库开发到复杂业务系统的完整技术能力。

特别擅长Vue3项目开发和前端组件库建设，能够独立完成复杂的前端架构设计和实现。具备良好的代码风格和注释习惯，能够编写高质量、易维护的代码。同时具备Node.js开发能力，能够编写构建工具和服务端应用。

期待在钛动科技发挥前端技术专长，为产品的用户体验和开发效率提升贡献力量。
