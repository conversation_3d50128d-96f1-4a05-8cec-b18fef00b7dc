# 个人简历 - 幻境游戏全栈开发工程师

## 个人信息
**姓 名：** 吴先生  **性 别：** 男  
**学 校：** 兰州大学  **学 历：** 硕士  **工作年限：** 10+  
**联系电话：** 13049380563  **电子邮件：** <EMAIL>  **期望薪资：** 面议  
**求职意向：** 全栈开发工程师（React/Node.js）  **求职状态：** 已离职  

## 教育背景
**2021.09 — 2024.06** 兰州大学 MBA  
**2009.09 — 2012.06** 湖南信息学院 计算机科学与技术  

## 核心技能
### 前端开发能力
1. **精通 React 生态系统**：熟练使用 React 18、React Hooks、React Router，具备高响应性用户界面组件开发经验
2. **TypeScript 专家级应用**：在大型项目中全面使用 TypeScript，提升代码质量和开发效率
3. **Next.js 全栈开发**：具备 SSR/SSG 项目开发经验，熟悉 Next.js 13+ App Router
4. **现代前端工程化**：精通 Webpack、Vite 构建工具，熟悉 CI/CD 流程和 Docker 容器化部署

### 后端开发能力
5. **Node.js/Express 服务端开发**：具备 REST API 设计与开发经验，熟悉数据库集成
6. **第三方 API 集成专家**：丰富的第三方服务集成经验（支付、地图、社交登录等）
7. **实时通信技术**：熟练使用 WebSocket、Socket.io 实现实时功能

### 游戏相关技术
8. **Canvas/WebGL 图形渲染**：具备 2D/3D 图形渲染和动画开发经验
9. **游戏状态管理**：熟悉游戏逻辑、状态同步、性能优化等游戏开发核心技术

## 工作经历

### 深圳鑫网泰科技有限公司
**岗位职责：** 高级全栈开发工程师  
**工作时间：** 2024/03 – 2025/04  
**工作描述：** 负责公司核心业务系统的全栈开发工作，完成复杂功能模块的设计与实现，参与技术方案制定和代码优化工作

## 项目经历

### 项目一：Shopify 跨境电商平台（2024/03 – 2025/04）
**★ 项目介绍**  
基于 React + Next.js 开发的跨境电商独立站，面向欧美市场，支持多语言、多货币，集成多种第三方支付方式。

**★ 技术亮点**  
- **React 高性能组件开发**：使用 React 18 + TypeScript 构建高响应性用户界面，实现商品展示、购物车、支付等核心功能
- **Next.js 全栈架构**：采用 Next.js 13 App Router，实现 SSR 优化，首屏渲染时间从 3.2s 优化到 1.8s
- **REST API 设计与开发**：使用 Node.js + Express 构建后端 API，实现用户认证、订单管理、支付处理等功能
- **第三方 API 深度集成**：集成 Shopify API、Stripe 支付、Google Analytics，实现完整电商生态
- **性能优化专家**：通过代码分割、懒加载、CDN 优化等手段，页面转化率提升 35%

**★ 使用技术栈**  
React.js、Next.js、TypeScript、Node.js、Express、Shopify API、Stripe、Webpack、Docker

### 项目二：电商 ERP 管理系统（2024/03 – 2025/04）
**★ 项目介绍**  
为跨境电商企业开发的一体化 ERP 管理系统，支持多平台订单同步，实现全流程数字化管理。

**★ 技术亮点**  
- **大数据量处理优化**：实现虚拟滚动技术，流畅展示 10 万+ 条数据记录，解决大数据渲染性能问题
- **实时数据同步**：基于 WebSocket 技术实现多用户协作时的数据一致性，减少数据冲突
- **微前端架构设计**：采用 qiankun 微前端框架，提升系统可维护性和扩展性
- **可复用组件库**：基于 ECharts 封装 20+ 种业务图表组件，提供丰富的数据可视化

**★ 使用技术栈**  
Vue.js 3、TypeScript、Node.js、WebSocket、qiankun、ECharts、Vite

### 项目三：兴盛优选电商平台（2021/05 – 2023/12）
**★ 项目介绍**  
千万级用户的电商核心业务平台，包含 PC 端管理后台和移动端用户界面。

**★ 技术亮点**  
- **Vue3 + TypeScript 企业级开发**：构建商品管理、订单处理、用户管理等核心模块
- **微信小程序开发**：实现完整购物流程，集成微信支付、支付宝等第三方支付
- **数据可视化大屏**：使用 ECharts 展示销售数据、用户行为分析等关键业务指标

**★ 使用技术栈**  
Vue3、TypeScript、微信小程序、Node.js、ECharts、Vite、Pinia

## 技术优势总结
- **10+ 年全栈开发经验**，具备从前端到后端的完整技术栈
- **React 生态专家**，熟悉最新的 React 18 特性和最佳实践
- **TypeScript 深度应用**，在大型项目中保证代码质量
- **API 开发与集成专家**，丰富的第三方服务集成经验
- **性能优化专家**，具备解决高并发、大数据量问题的实践经验
- **名校硕士学历**，具备良好的学习能力和技术视野
- **英文文档阅读能力强**，能够快速学习新技术
