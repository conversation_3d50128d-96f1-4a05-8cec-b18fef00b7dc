# 个人简历 - 钛动科技前端架构师

## 个人信息
**姓 名：** 吴先生  **性 别：** 男  
**学 校：** 兰州大学  **学 历：** 硕士  **工作年限：** 10+  
**联系电话：** 13049380563  **电子邮件：** <EMAIL>  **期望薪资：** 面议  
**求职意向：** 高级前端开发工程师  **求职状态：** 已离职  

## 教育背景
**2021.09 — 2024.06** 兰州大学 MBA  
**2009.09 — 2012.06** 湖南信息学院 计算机科学与技术（计算机相关专业）  

## 核心技术能力
### 前端技术基础
1. **前端基础技术精通**：深度掌握 HTML5、CSS3、JavaScript ES6+，具备扎实的前端技术基础
2. **主流框架专家**：精通 React、Vue2/Vue3 等主流前端技术栈，具备框架原理深度理解
3. **Vue3 项目开发专家**：在多个大型项目中使用 Vue3 + Composition API，具备丰富的实战经验
4. **TypeScript 企业级应用**：在大型项目中全面使用 TypeScript，提升代码质量和可维护性

### 前端架构设计能力
5. **复杂前端架构设计**：能够独立完成大型前端系统的架构设计和实现
6. **前端组件库开发**：具备从 0 到 1 构建企业级组件库的完整经验
7. **微前端架构实践**：使用 qiankun 实现微前端架构，支持多团队协作开发
8. **性能优化专家**：具备前端性能优化的丰富经验，包括首屏优化、懒加载、缓存策略等

### 工程化与工具链
9. **现代构建工具**：精通 Webpack、Vite、Rollup 等构建工具，具备工程化配置经验
10. **Node.js 脚本开发**：具备使用 Node.js 编写构建脚本、工具链和服务端应用的能力
11. **代码质量保障**：建立完善的代码规范体系，使用 ESLint、Prettier、Husky 等工具

## 工作经历

### 深圳鑫网泰科技有限公司
**岗位职责：** 前端架构师 / 高级前端开发工程师  
**工作时间：** 2024/03 – 2025/04  
**工作描述：** 负责前端架构设计，领导前端团队完成复杂业务功能开发，制定前端技术规范

### 湖南兴盛优选网络科技有限公司
**岗位职责：** 高级前端开发工程师  
**工作时间：** 2021/05 – 2023/12  
**工作描述：** 负责电商平台前端开发，参与系统架构设计和性能优化

## 核心项目经历

### 项目一：企业级组件库开发（2024/03 – 2025/04）
**★ 项目介绍**  
从 0 到 1 构建企业级 Vue3 组件库，支持公司多个业务线使用，包含 50+ 个业务组件和基础组件。

**★ 技术架构亮点**  
- **Vue3 + TypeScript 架构**：使用 Vue3 Composition API + TypeScript 构建类型安全的组件库
- **组件设计系统**：建立完整的设计系统，包含颜色、字体、间距、动效等设计规范
- **高质量组件开发**：开发表格、表单、图表、上传等 50+ 个高复用性组件
- **文档与测试体系**：使用 VitePress 构建组件文档，Jest + Vue Test Utils 实现单元测试
- **构建与发布**：使用 Vite + Rollup 构建，支持 ES Module、CommonJS 多种格式输出

**★ 业务价值**  
- 组件库被 5+ 个业务线采用，开发效率提升 40%
- 统一了公司前端技术栈和设计规范
- 减少重复开发，代码复用率达到 80%

**★ 使用技术栈**  
Vue3、TypeScript、Vite、Rollup、VitePress、Jest、Less

### 项目二：电商 ERP 前端架构重构（2024/03 – 2025/04）
**★ 项目介绍**  
对传统电商 ERP 系统进行前端架构重构，采用 Vue3 + 微前端架构，支持多团队并行开发。

**★ 前端架构设计**  
- **Vue3 技术栈升级**：从 Vue2 升级到 Vue3，使用 Composition API 重构核心业务逻辑
- **微前端架构**：使用 qiankun 实现微前端架构，将系统拆分为 8 个独立的子应用
- **状态管理优化**：使用 Pinia 替代 Vuex，实现更简洁的状态管理
- **性能优化方案**：实现虚拟滚动、懒加载、代码分割等性能优化，支持 10 万+ 数据展示

**★ 工程化建设**  
- **统一构建配置**：使用 Vite 统一构建配置，构建速度提升 60%
- **代码规范体系**：建立 ESLint + Prettier + Husky 代码规范体系
- **自动化测试**：建立单元测试和 E2E 测试体系，测试覆盖率达到 85%

**★ 使用技术栈**  
Vue3、TypeScript、qiankun、Pinia、Vite、Ant Design Vue、ECharts

### 项目三：兴盛优选电商平台前端重构（2021/05 – 2023/12）
**★ 项目介绍**  
负责兴盛优选核心电商平台的前端架构设计和开发，支撑千万级用户的日常购物需求。

**★ 复杂前端架构实现**  
- **Vue3 企业级应用**：使用 Vue3 + TypeScript 重构商品管理、订单系统、用户中心等核心模块
- **大数据量处理**：实现虚拟滚动技术，解决大数据量列表渲染性能问题
- **实时数据同步**：基于 WebSocket 实现订单状态、库存信息的实时更新
- **移动端适配**：使用 Vant 组件库开发移动端 H5 页面，实现响应式设计

**★ 性能优化成果**  
- 页面首屏渲染时间优化 50%
- 大数据列表滚动性能提升 80%
- 移动端页面加载速度提升 40%

**★ 使用技术栈**  
Vue3、TypeScript、Vant、Element Plus、ECharts、WebSocket、Vite

### 项目四：供应链物流管理系统（2022/01 – 2023/12）
**★ 项目介绍**  
构建全国性供应链物流管理系统前端，覆盖 30+ 仓储中心，支持复杂的物流业务流程。

**★ 前端架构特色**  
- **微前端实践**：使用 qiankun 实现仓库管理、运输调度、数据报表等模块的独立开发和部署
- **移动端 PDA 系统**：使用 Vue3 + Vant 开发移动端 PDA 操作界面，支持扫码、拣货等仓储作业
- **地图可视化**：集成百度地图 API 实现配送路径规划和实时位置追踪
- **数据可视化**：使用 ECharts 构建物流数据大屏，实时展示业务指标

**★ 使用技术栈**  
Vue3、qiankun、Vant、百度地图 API、ECharts、WebSocket

## 技术专长总结
- **10+ 年前端开发经验**，具备从初级到架构师的完整成长路径
- **Vue3 技术专家**，在多个大型项目中深度应用 Vue3 生态
- **前端组件库开发专家**，具备从设计到实现的完整组件库开发经验
- **前端架构设计能力**，能够独立完成复杂前端系统的架构设计
- **高质量代码实践者**，具备良好的代码风格和注释习惯
- **性能优化专家**，在多个项目中实现显著的性能提升
- **团队协作能力强**，具备与产品、设计、后端的良好沟通协作经验
